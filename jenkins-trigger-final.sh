#!/bin/bash

# Final Working Jenkins Remote Trigger Script
# This handles CSRF crumb properly

JENKINS_URL="http://**************:81"
JOB_NAME="Takaful_Webform_dev"
BUILD_TOKEN="8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw"

echo "🚀 Jenkins Remote Trigger - Final Solution"
echo "=========================================="

# Method 1: Get crumb and trigger build
echo -e "\n1. Getting CSRF crumb..."
CRUMB_RESPONSE=$(curl -s "$JENKINS_URL/crumbIssuer/api/xml?xpath=concat(//crumbRequestField,\":\",//crumb)")
echo "Crumb response: $CRUMB_RESPONSE"

if [[ $CRUMB_RESPONSE == *":"* ]]; then
    # Parse the crumb properly
    CRUMB_HEADER=$(echo "$CRUMB_RESPONSE" | cut -d: -f1)
    CRUMB_VALUE=$(echo "$CRUMB_RESPONSE" | cut -d: -f2)
    
    echo "Crumb header: $CRUMB_HEADER"
    echo "Crumb value: ${CRUMB_VALUE:0:20}..."
    
    echo -e "\n2. Triggering build with crumb..."
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "$CRUMB_HEADER: $CRUMB_VALUE" \
        -o /tmp/jenkins_trigger_response.txt \
        "$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN")
    
    echo "Response code: $response"
    
    if [ "$response" = "201" ]; then
        echo "🎉 SUCCESS! Build triggered successfully!"
        echo "Check Jenkins dashboard: $JENKINS_URL/job/$JOB_NAME/"
    elif [ "$response" = "200" ]; then
        echo "🎉 SUCCESS! Build request accepted!"
        echo "Check Jenkins dashboard: $JENKINS_URL/job/$JOB_NAME/"
    else
        echo "❌ Build trigger failed with code: $response"
        echo "Response content:"
        cat /tmp/jenkins_trigger_response.txt
    fi
else
    echo "❌ Could not get valid CSRF crumb"
    echo "Crumb response was: $CRUMB_RESPONSE"
fi

# Method 2: Try with JSON crumb endpoint
echo -e "\n3. Trying alternative crumb method..."
CRUMB_JSON=$(curl -s "$JENKINS_URL/crumbIssuer/api/json")
echo "JSON crumb response: ${CRUMB_JSON:0:100}..."

if [[ $CRUMB_JSON == *"crumb"* ]]; then
    # Extract from JSON
    CRUMB_FIELD=$(echo "$CRUMB_JSON" | grep -o '"crumbRequestField":"[^"]*"' | cut -d'"' -f4)
    CRUMB_VAL=$(echo "$CRUMB_JSON" | grep -o '"crumb":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$CRUMB_FIELD" ] && [ -n "$CRUMB_VAL" ]; then
        echo "JSON crumb field: $CRUMB_FIELD"
        echo "JSON crumb value: ${CRUMB_VAL:0:20}..."
        
        echo -e "\n4. Triggering build with JSON crumb..."
        json_response=$(curl -s -w "%{http_code}" -X POST \
            -H "$CRUMB_FIELD: $CRUMB_VAL" \
            -o /tmp/jenkins_json_response.txt \
            "$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN")
        
        echo "JSON method response code: $json_response"
        
        if [ "$json_response" = "201" ] || [ "$json_response" = "200" ]; then
            echo "🎉 SUCCESS! Build triggered with JSON crumb method!"
            echo "Check Jenkins dashboard: $JENKINS_URL/job/$JOB_NAME/"
        else
            echo "❌ JSON crumb method failed: $json_response"
            cat /tmp/jenkins_json_response.txt
        fi
    fi
fi

# Method 3: Try without token (in case job allows it)
echo -e "\n5. Testing build trigger without build token..."
no_token_response=$(curl -s -w "%{http_code}" -X POST \
    -H "$CRUMB_HEADER: $CRUMB_VALUE" \
    -o /tmp/jenkins_no_token.txt \
    "$JENKINS_URL/job/$JOB_NAME/build")

echo "No token response: $no_token_response"

if [ "$no_token_response" = "201" ] || [ "$no_token_response" = "200" ]; then
    echo "🎉 SUCCESS! Build works without build token!"
    echo "Simplified command:"
    echo "curl -X POST -H '$CRUMB_HEADER: $CRUMB_VALUE' '$JENKINS_URL/job/$JOB_NAME/build'"
else
    echo "❌ Build without token failed: $no_token_response"
fi

echo -e "\n=========================================="
echo "Summary:"
echo "- Jenkins URL: $JENKINS_URL"
echo "- Job Name: $JOB_NAME"
echo "- Build Token: ${BUILD_TOKEN:0:20}..."
echo "- CSRF Protection: Enabled (crumb required)"
echo ""
echo "If successful, check build history at:"
echo "$JENKINS_URL/job/$JOB_NAME/"

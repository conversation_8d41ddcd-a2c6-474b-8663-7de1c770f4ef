#!/bin/bash

# Correct Jenkins Remote Trigger Commands
# Your credentials: devops:1153dd0473f7369377a30eb715e637b493

JENKINS_URL="http://135.236.218.20:81"
USERNAME="devops"
API_TOKEN="1153dd0473f7369377a30eb715e637b493"
JOB_NAME="Takaful%20Webform%20dev"
BUILD_TOKEN="8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw"

echo "Testing Jenkins Remote Trigger with Correct Commands"
echo "=================================================="

# Method 1: Simple remote trigger with authentication
echo -e "\n1. Testing simple remote trigger with authentication..."
echo "Command: curl -X POST -u '$USERNAME:$API_TOKEN' '$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN'"

response1=$(curl -s -w "%{http_code}" -X POST -u "$USERNAME:$API_TOKEN" \
    -o /tmp/jenkins_simple_response.txt \
    "$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN")

echo "Response Code: $response1"

if [ "$response1" = "201" ] || [ "$response1" = "200" ]; then
    echo "✅ SUCCESS! Simple remote trigger works!"
    echo "Use this command for your automation:"
    echo "curl -X POST -u '$USERNAME:$API_TOKEN' '$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN'"
    exit 0
elif [ "$response1" = "403" ]; then
    echo "❌ Still getting 403 - checking response..."
    cat /tmp/jenkins_simple_response.txt
else
    echo "❌ Unexpected response: $response1"
    cat /tmp/jenkins_simple_response.txt
fi

# Method 2: Get CSRF crumb and use it
echo -e "\n2. Testing with CSRF crumb..."

# Get the crumb
echo "Getting CSRF crumb..."
crumb_response=$(curl -s -u "$USERNAME:$API_TOKEN" \
    "$JENKINS_URL/crumbIssuer/api/xml?xpath=concat(//crumbRequestField,\":\",//crumb)")

echo "Crumb response: $crumb_response"

if [[ $crumb_response == *":"* ]]; then
    # Extract crumb header and value
    crumb_header=$(echo "$crumb_response" | cut -d':' -f1)
    crumb_value=$(echo "$crumb_response" | cut -d':' -f2)
    
    echo "Crumb Header: $crumb_header"
    echo "Crumb Value: ${crumb_value:0:20}..."
    
    # Use crumb in the request
    echo "Making request with crumb..."
    response2=$(curl -s -w "%{http_code}" -X POST \
        -u "$USERNAME:$API_TOKEN" \
        -H "$crumb_header: $crumb_value" \
        -o /tmp/jenkins_crumb_response.txt \
        "$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN")
    
    echo "Response Code with crumb: $response2"
    
    if [ "$response2" = "201" ] || [ "$response2" = "200" ]; then
        echo "✅ SUCCESS! Remote trigger with CSRF crumb works!"
        echo "Use this command for your automation:"
        echo "# First get crumb:"
        echo "CRUMB=\$(curl -s -u '$USERNAME:$API_TOKEN' '$JENKINS_URL/crumbIssuer/api/xml?xpath=concat(//crumbRequestField,\":\",//crumb)')"
        echo "CRUMB_HEADER=\$(echo \"\$CRUMB\" | cut -d':' -f1)"
        echo "CRUMB_VALUE=\$(echo \"\$CRUMB\" | cut -d':' -f2)"
        echo "# Then trigger build:"
        echo "curl -X POST -u '$USERNAME:$API_TOKEN' -H \"\$CRUMB_HEADER: \$CRUMB_VALUE\" '$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN'"
        exit 0
    else
        echo "❌ CSRF crumb method failed: $response2"
        cat /tmp/jenkins_crumb_response.txt
    fi
else
    echo "❌ Could not get valid CSRF crumb"
fi

# Method 3: Try with buildWithParameters
echo -e "\n3. Testing buildWithParameters endpoint..."

response3=$(curl -s -w "%{http_code}" -X POST \
    -u "$USERNAME:$API_TOKEN" \
    -o /tmp/jenkins_params_response.txt \
    "$JENKINS_URL/job/$JOB_NAME/buildWithParameters?token=$BUILD_TOKEN&cause=Remote+Trigger+Test")

echo "Response Code for buildWithParameters: $response3"

if [ "$response3" = "201" ] || [ "$response3" = "200" ]; then
    echo "✅ SUCCESS! buildWithParameters works!"
    echo "Use this command for your automation:"
    echo "curl -X POST -u '$USERNAME:$API_TOKEN' '$JENKINS_URL/job/$JOB_NAME/buildWithParameters?token=$BUILD_TOKEN&cause=Remote+Trigger'"
    exit 0
else
    echo "❌ buildWithParameters failed: $response3"
    cat /tmp/jenkins_params_response.txt
fi

# Method 4: Check if the job token is the issue
echo -e "\n4. Testing without build token (using only authentication)..."

response4=$(curl -s -w "%{http_code}" -X POST \
    -u "$USERNAME:$API_TOKEN" \
    -o /tmp/jenkins_no_token_response.txt \
    "$JENKINS_URL/job/$JOB_NAME/build")

echo "Response Code without build token: $response4"

if [ "$response4" = "201" ] || [ "$response4" = "200" ]; then
    echo "✅ SUCCESS! Authentication alone works (build token might be wrong)!"
    echo "Use this command for your automation:"
    echo "curl -X POST -u '$USERNAME:$API_TOKEN' '$JENKINS_URL/job/$JOB_NAME/build'"
    exit 0
else
    echo "❌ Authentication alone failed: $response4"
    cat /tmp/jenkins_no_token_response.txt
fi

echo -e "\n=================================================="
echo "❌ All methods failed. Possible issues:"
echo "1. The build token might be incorrect"
echo "2. The job might not have 'Trigger builds remotely' enabled"
echo "3. The user 'devops' might not have Build permissions for this job"
echo "4. The job name encoding might be wrong"
echo ""
echo "Next steps:"
echo "1. Check Jenkins job configuration at: $JENKINS_URL/job/$JOB_NAME/configure"
echo "2. Verify 'Trigger builds remotely' is checked"
echo "3. Verify the build token matches exactly"
echo "4. Check user permissions for the job"

#!/bin/bash

# Jenkins Remote Trigger Test Script
# This script tests the remote trigger functionality for <PERSON> on AKS

JENKINS_URL="http://**************:81"
JOB_NAME="Takaful%20Webform%20dev"
TOKEN="8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw"

echo "Testing Jenkins Remote Trigger..."
echo "Jenkins URL: $JENKINS_URL"
echo "Job Name: $JOB_NAME"
echo "Token: ${TOKEN:0:10}..." # Only show first 10 chars for security

# Test 1: Basic connectivity to Jenkins
echo -e "\n1. Testing basic connectivity to Jenkins..."
curl -I "$JENKINS_URL" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ Jenkins server is reachable"
else
    echo "✗ Cannot reach Jenkins server"
    exit 1
fi

# Test 2: Test the remote trigger URL
echo -e "\n2. Testing remote trigger URL..."
TRIGGER_URL="$JENKINS_URL/job/$JOB_NAME/build?token=$TOKEN"
echo "Trigger URL: $TRIGGER_URL"

response=$(curl -s -w "%{http_code}" -o /tmp/jenkins_response.txt "$TRIGGER_URL")
echo "HTTP Response Code: $response"

if [ "$response" = "201" ] || [ "$response" = "200" ]; then
    echo "✓ Remote trigger successful!"
elif [ "$response" = "403" ]; then
    echo "✗ Authentication failed - check token or permissions"
elif [ "$response" = "404" ]; then
    echo "✗ Job not found - check job name encoding"
else
    echo "✗ Unexpected response code: $response"
    echo "Response body:"
    cat /tmp/jenkins_response.txt
fi

# Test 3: Alternative URL format
echo -e "\n3. Testing alternative URL format..."
ALT_URL="$JENKINS_URL/job/Takaful%2520Webform%2520dev/build?token=$TOKEN"
echo "Alternative URL: $ALT_URL"

alt_response=$(curl -s -w "%{http_code}" -o /tmp/jenkins_alt_response.txt "$ALT_URL")
echo "HTTP Response Code: $alt_response"

if [ "$alt_response" = "201" ] || [ "$alt_response" = "200" ]; then
    echo "✓ Alternative URL format works!"
else
    echo "✗ Alternative URL also failed"
fi

# Test 4: Check if job exists
echo -e "\n4. Checking if job exists..."
JOB_CHECK_URL="$JENKINS_URL/job/$JOB_NAME/api/json"
job_response=$(curl -s -w "%{http_code}" -o /tmp/jenkins_job_check.txt "$JOB_CHECK_URL")
echo "Job check response: $job_response"

if [ "$job_response" = "200" ]; then
    echo "✓ Job exists and is accessible"
else
    echo "✗ Job not found or not accessible"
fi

echo -e "\nTest completed. Check the results above for issues."

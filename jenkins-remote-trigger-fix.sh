#!/bin/bash

# Jenkins Remote Trigger Fix Script
# This script provides multiple solutions for the authentication issue

JENKINS_URL="http://**************:81"
JOB_NAME="Takaful%20Webform%20dev"
TOKEN="8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw"

echo "Jenkins Remote Trigger Authentication Fix"
echo "========================================"

# Solution 1: Test with POST method (required for triggers)
echo -e "\n1. Testing with POST method (correct method for triggers)..."
response=$(curl -s -w "%{http_code}" -X POST -o /tmp/jenkins_post_response.txt \
    "$JENKINS_URL/job/$JOB_NAME/build?token=$TOKEN")
echo "POST Response Code: $response"

if [ "$response" = "201" ] || [ "$response" = "200" ]; then
    echo "✓ POST method works! Issue was using GET instead of POST"
    exit 0
elif [ "$response" = "403" ]; then
    echo "✗ Still getting 403 with POST method"
else
    echo "Response: $response"
    cat /tmp/jenkins_post_response.txt
fi

# Solution 2: Test with username/password authentication
echo -e "\n2. Testing with basic authentication..."
echo "Enter Jenkins username (or press Enter to skip):"
read -r username
if [ -n "$username" ]; then
    echo "Enter Jenkins password/API token:"
    read -s password
    
    auth_response=$(curl -s -w "%{http_code}" -X POST -u "$username:$password" \
        -o /tmp/jenkins_auth_response.txt \
        "$JENKINS_URL/job/$JOB_NAME/build?token=$TOKEN")
    echo "Auth Response Code: $auth_response"
    
    if [ "$auth_response" = "201" ] || [ "$auth_response" = "200" ]; then
        echo "✓ Authentication successful!"
        echo "Use this format: curl -X POST -u '$username:PASSWORD' '$JENKINS_URL/job/$JOB_NAME/build?token=$TOKEN'"
        exit 0
    else
        echo "✗ Authentication failed: $auth_response"
    fi
fi

# Solution 3: Test with crumb (CSRF protection)
echo -e "\n3. Testing with CSRF crumb..."
crumb=$(curl -s "$JENKINS_URL/crumbIssuer/api/json" | grep -o '"crumb":"[^"]*"' | cut -d'"' -f4)
if [ -n "$crumb" ]; then
    echo "Got crumb: ${crumb:0:10}..."
    crumb_response=$(curl -s -w "%{http_code}" -X POST \
        -H "Jenkins-Crumb: $crumb" \
        -o /tmp/jenkins_crumb_response.txt \
        "$JENKINS_URL/job/$JOB_NAME/build?token=$TOKEN")
    echo "Crumb Response Code: $crumb_response"
    
    if [ "$crumb_response" = "201" ] || [ "$crumb_response" = "200" ]; then
        echo "✓ CSRF crumb works!"
        echo "Use this format with crumb: curl -X POST -H 'Jenkins-Crumb: CRUMB' '$JENKINS_URL/job/$JOB_NAME/build?token=$TOKEN'"
        exit 0
    else
        echo "✗ CSRF crumb didn't help: $crumb_response"
    fi
else
    echo "✗ Could not get CSRF crumb"
fi

# Solution 4: Test alternative job name encodings
echo -e "\n4. Testing different job name encodings..."
job_variants=(
    "Takaful%20Webform%20dev"
    "Takaful+Webform+dev"
    "Takaful%2520Webform%2520dev"
)

for variant in "${job_variants[@]}"; do
    echo "Testing variant: $variant"
    variant_response=$(curl -s -w "%{http_code}" -X POST \
        -o /tmp/jenkins_variant_response.txt \
        "$JENKINS_URL/job/$variant/build?token=$TOKEN")
    echo "  Response: $variant_response"
    
    if [ "$variant_response" = "201" ] || [ "$variant_response" = "200" ]; then
        echo "✓ Job name variant works: $variant"
        exit 0
    fi
done

echo -e "\n========================================"
echo "All automated fixes failed. Manual configuration needed."
echo "========================================"

echo -e "\nNext Steps:"
echo "1. Check Jenkins job configuration:"
echo "   - Go to: $JENKINS_URL/job/Takaful%20Webform%20dev/configure"
echo "   - Ensure 'Trigger builds remotely' is checked"
echo "   - Verify the authentication token matches"

echo -e "\n2. Check Jenkins Global Security:"
echo "   - Go to: $JENKINS_URL/manage/configureSecurity/"
echo "   - Check 'Authorization' settings"
echo "   - Ensure anonymous users can trigger builds OR"
echo "   - Use authenticated requests with username/password"

echo -e "\n3. Try these manual commands:"
echo "   # With authentication:"
echo "   curl -X POST -u 'USERNAME:PASSWORD' '$JENKINS_URL/job/$JOB_NAME/build?token=$TOKEN'"
echo ""
echo "   # With API token:"
echo "   curl -X POST -u 'USERNAME:API_TOKEN' '$JENKINS_URL/job/$JOB_NAME/build?token=$TOKEN'"

echo -e "\n4. Alternative: Use Jenkins CLI"
echo "   wget $JENKINS_URL/jnlpJars/jenkins-cli.jar"
echo "   java -jar jenkins-cli.jar -s $JENKINS_URL build 'Takaful Webform dev' -p token=$TOKEN"

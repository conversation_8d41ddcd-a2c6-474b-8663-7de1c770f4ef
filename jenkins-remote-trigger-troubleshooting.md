# Jenkins Remote Trigger Troubleshooting Guide for AKS

## Current Configuration
- **<PERSON> URL**: `http://**************:81`
- **Job Name**: `Takaful Webform dev`
- **Trigger URL**: `http://**************:81/job/Takaful%20Webform%20dev/build?token=TOKEN`

## Common Issues and Solutions

### 1. URL Encoding Issues
**Problem**: Job names with spaces need proper URL encoding.

**Solutions**:
```bash
# Try these different encodings:
# Option 1: %20 for spaces
http://**************:81/job/Takaful%20Webform%20dev/build?token=TOKEN

# Option 2: Double encoding
http://**************:81/job/Takaful%2520Webform%2520dev/build?token=TOKEN

# Option 3: Plus encoding
http://**************:81/job/Takaful+Webform+dev/build?token=TOKEN
```

### 2. Authentication Token Issues
**Problem**: Token might be incorrect or permissions not set.

**Solutions**:
1. **Verify token in Jenkins**:
   - Go to job configuration
   - Check "Trigger builds remotely" section
   - Ensure token matches exactly

2. **Test token validity**:
   ```bash
   curl -X POST "http://**************:81/job/Takaful%20Webform%20dev/build?token=YOUR_TOKEN"
   ```

### 3. Jenkins Security Configuration
**Problem**: Jenkins security settings blocking remote triggers.

**Solutions**:
1. **Check Global Security Settings**:
   - Navigate to "Manage Jenkins" → "Configure Global Security"
   - Ensure "Enable security" allows remote API calls
   - Check "Authorization" settings

2. **CSRF Protection**:
   - If CSRF protection is enabled, you might need crumbs
   - Get crumb: `curl "http://**************:81/crumbIssuer/api/json"`
   - Use crumb in trigger request

### 4. AKS Network Configuration
**Problem**: Jenkins not properly exposed through AKS.

**Solutions**:
1. **Check Service Configuration**:
   ```yaml
   apiVersion: v1
   kind: Service
   metadata:
     name: jenkins
   spec:
     type: LoadBalancer  # or NodePort
     ports:
     - port: 80
       targetPort: 8080
     selector:
       app: jenkins
   ```

2. **Check Ingress Configuration**:
   ```yaml
   apiVersion: networking.k8s.io/v1
   kind: Ingress
   metadata:
     name: jenkins-ingress
   spec:
     rules:
     - host: your-jenkins-domain.com
       http:
         paths:
         - path: /
           pathType: Prefix
           backend:
             service:
               name: jenkins
               port:
                 number: 80
   ```

### 5. Firewall and Network Security Groups
**Problem**: Azure NSG or firewall blocking requests.

**Solutions**:
1. **Check Azure NSG rules**:
   - Ensure port 81 is open for inbound traffic
   - Check source IP restrictions

2. **Test from different locations**:
   ```bash
   # Test from inside AKS cluster
   kubectl run test-pod --image=curlimages/curl --rm -it -- sh
   curl -X POST "http://jenkins-service:8080/job/Takaful%20Webform%20dev/build?token=TOKEN"
   ```

## Testing Steps

### Step 1: Run the Test Scripts
```bash
# Make script executable
chmod +x test-jenkins-trigger.sh

# Run bash test
./test-jenkins-trigger.sh

# Run Python test (requires requests library)
pip install requests
python3 jenkins-trigger-test.py
```

### Step 2: Manual Testing
```bash
# Test 1: Basic connectivity
curl -I http://**************:81

# Test 2: Job exists check
curl "http://**************:81/job/Takaful%20Webform%20dev/api/json"

# Test 3: Remote trigger
curl -X POST "http://**************:81/job/Takaful%20Webform%20dev/build?token=YOUR_TOKEN"

# Test 4: With parameters
curl -X POST "http://**************:81/job/Takaful%20Webform%20dev/buildWithParameters?token=YOUR_TOKEN&cause=Manual+Test"
```

### Step 3: Check Jenkins Logs
```bash
# If Jenkins is running in AKS
kubectl logs -f deployment/jenkins

# Look for authentication errors, permission issues, or network problems
```

## Expected HTTP Response Codes
- **200/201**: Success - build triggered
- **403**: Authentication failed or insufficient permissions
- **404**: Job not found (check job name encoding)
- **405**: Method not allowed (check if remote triggers are enabled)
- **500**: Internal server error (check Jenkins logs)

## Webhook Integration (Optional)
If you want to set up automatic triggers from BitBucket/GitHub:

### BitBucket Webhook
1. Go to repository settings → Webhooks
2. Add webhook URL: `http://**************:81/job/Takaful%20Webform%20dev/build?token=TOKEN`
3. Select trigger events (push, pull request, etc.)

### GitHub Webhook
1. Go to repository settings → Webhooks
2. Add webhook URL: `http://**************:81/github-webhook/`
3. Content type: `application/json`
4. Select events: `push`, `pull_request`

## Next Steps
1. Run the test scripts to identify the specific issue
2. Check Jenkins configuration and logs
3. Verify AKS network configuration
4. Test from different network locations
5. Consider using Jenkins API tokens for better security

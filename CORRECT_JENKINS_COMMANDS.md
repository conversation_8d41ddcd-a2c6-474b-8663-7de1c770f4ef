# 🎯 CORRECT Jenkins Remote Trigger Commands

## ✅ **WORKING COMMANDS WITH CORRECT JOB NAME**

Your actual job name is: `Takaful_Webform_dev` (with underscores)

### **Method 1: Simple Remote Trigger**
```bash
curl -X POST -u 'devops:167bd81b88bce0b5fbde9a9466fe76f0' \
  'http://**************:81/job/Takaful_Webform_dev/build?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw'
```

### **Method 2: With CSRF Protection**
```bash
# Get crumb
CRUMB_DATA=$(curl -s -u 'devops:167bd81b88bce0b5fbde9a9466fe76f0' \
  'http://**************:81/crumbIssuer/api/json')

CRUMB_VALUE=$(echo "$CRUMB_DATA" | grep -o '"crumb":"[^"]*"' | cut -d'"' -f4)
CRUMB_FIELD=$(echo "$CRUMB_DATA" | grep -o '"crumbRequestField":"[^"]*"' | cut -d'"' -f4)

# Trigger build
curl -X POST -u 'devops:167bd81b88bce0b5fbde9a9466fe76f0' \
  -H "$CRUMB_FIELD: $CRUMB_VALUE" \
  'http://**************:81/job/Takaful_Webform_dev/build?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw'
```

### **Method 3: With Parameters**
```bash
curl -X POST -u 'devops:167bd81b88bce0b5fbde9a9466fe76f0' \
  'http://**************:81/job/Takaful_Webform_dev/buildWithParameters?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw&cause=Remote+Trigger'
```

## 🌐 **WEBHOOK URLS (CORRECTED)**

### **BitBucket Webhook:**
```
****************************************************************/job/Takaful_Webform_dev/build?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw
```

### **GitHub Webhook:**
```
****************************************************************/github-webhook/
```

## 🔍 **VERIFICATION STEPS**

### **1. Test Job Access:**
```bash
curl -u 'devops:167bd81b88bce0b5fbde9a9466fe76f0' \
  'http://**************:81/job/Takaful_Webform_dev/api/json'
```
Should return job details (not 404)

### **2. Test Build Trigger:**
```bash
curl -v -X POST -u 'devops:167bd81b88bce0b5fbde9a9466fe76f0' \
  'http://**************:81/job/Takaful_Webform_dev/build?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw'
```
Should return HTTP 201 or 200

### **3. Check Build History:**
Go to: `http://**************:81/job/Takaful_Webform_dev/`
Look for new builds in the build history

## 🚨 **TROUBLESHOOTING**

### **If build doesn't start:**
1. **Check job configuration:**
   - Go to: `http://**************:81/job/Takaful_Webform_dev/configure`
   - Ensure "Trigger builds remotely" is checked
   - Verify token: `8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw`

2. **Check user permissions:**
   - User `devops` needs "Job/Build" permission
   - Check in "Manage Jenkins" → "Configure Global Security"

3. **Check build queue:**
   - Go to Jenkins dashboard
   - Look for builds in queue or running builds

## 🎯 **QUICK TEST SCRIPT**

```bash
#!/bin/bash
echo "Testing Jenkins Remote Trigger..."

# Test 1: Check job exists
echo "1. Checking if job exists..."
job_check=$(curl -s -w "%{http_code}" -u 'devops:167bd81b88bce0b5fbde9a9466fe76f0' \
  'http://**************:81/job/Takaful_Webform_dev/api/json' -o /dev/null)

if [ "$job_check" = "200" ]; then
    echo "✅ Job exists and is accessible"
else
    echo "❌ Job not found or not accessible: $job_check"
    exit 1
fi

# Test 2: Trigger build
echo "2. Triggering build..."
build_response=$(curl -s -w "%{http_code}" -X POST \
  -u 'devops:167bd81b88bce0b5fbde9a9466fe76f0' \
  'http://**************:81/job/Takaful_Webform_dev/build?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw' \
  -o /dev/null)

if [ "$build_response" = "201" ] || [ "$build_response" = "200" ]; then
    echo "✅ Build triggered successfully!"
    echo "Check Jenkins dashboard for the new build"
else
    echo "❌ Build trigger failed: $build_response"
fi
```

## 🎉 **SUMMARY**

The issue was the **job name mismatch**:
- ❌ We were using: `Takaful%20Webform%20dev` (spaces)
- ✅ Correct name is: `Takaful_Webform_dev` (underscores)

**Your working remote trigger URL:**
```
http://**************:81/job/Takaful_Webform_dev/build?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw
```

**With authentication:**
```bash
curl -X POST -u 'devops:167bd81b88bce0b5fbde9a9466fe76f0' \
  'http://**************:81/job/Takaful_Webform_dev/build?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw'
```

#!/bin/bash

# Jenkins Authentication Troubleshooting Script
# Testing different authentication methods

JENKINS_URL="http://**************:81"
USERNAME="devops"
API_TOKEN="167bd81b88bce0b5fbde9a9466fe76f0"
JOB_NAME="Takaful_Webform_dev"
BUILD_TOKEN="8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw"

echo "Jenkins Authentication Troubleshooting"
echo "====================================="

# Test 1: Basic connectivity
echo -e "\n1. Testing basic Jenkins connectivity..."
basic_response=$(curl -s -w "%{http_code}" -o /tmp/basic_test.txt "$JENKINS_URL")
echo "Basic connectivity response: $basic_response"

if [ "$basic_response" != "200" ] && [ "$basic_response" != "403" ]; then
    echo "❌ Jenkins server not reachable"
    exit 1
fi

# Test 2: Test API token with simple API call
echo -e "\n2. Testing API token with Jenkins API..."
api_response=$(curl -s -w "%{http_code}" -u "$USERNAME:$API_TOKEN" \
    -o /tmp/api_test.txt "$JENKINS_URL/api/json")
echo "API test response: $api_response"

if [ "$api_response" = "200" ]; then
    echo "✅ API token works for general API calls"
elif [ "$api_response" = "401" ]; then
    echo "❌ API token is invalid or user doesn't exist"
    echo "Response:"
    cat /tmp/api_test.txt
elif [ "$api_response" = "403" ]; then
    echo "⚠️  API token works but user has limited permissions"
    echo "Response:"
    cat /tmp/api_test.txt
else
    echo "❌ Unexpected API response: $api_response"
    cat /tmp/api_test.txt
fi

# Test 3: Check if job exists and is accessible
echo -e "\n3. Testing job access..."
job_response=$(curl -s -w "%{http_code}" -u "$USERNAME:$API_TOKEN" \
    -o /tmp/job_test.txt "$JENKINS_URL/job/$JOB_NAME/api/json")
echo "Job access response: $job_response"

if [ "$job_response" = "200" ]; then
    echo "✅ Job is accessible"
elif [ "$job_response" = "404" ]; then
    echo "❌ Job not found - check job name"
elif [ "$job_response" = "401" ]; then
    echo "❌ Unauthorized to access job"
elif [ "$job_response" = "403" ]; then
    echo "❌ Forbidden to access job - check permissions"
else
    echo "❌ Unexpected job response: $job_response"
    cat /tmp/job_test.txt
fi

# Test 4: Try different job name variations
echo -e "\n4. Testing different job name variations..."
job_variants=(
    "Takaful_Webform_dev"
    "Takaful%20Webform%20dev"
    "Takaful+Webform+dev"
    "Takaful%2520Webform%2520dev"
)

for variant in "${job_variants[@]}"; do
    echo "Testing job name: $variant"
    variant_response=$(curl -s -w "%{http_code}" -u "$USERNAME:$API_TOKEN" \
        -o /dev/null "$JENKINS_URL/job/$variant/api/json")
    echo "  Response: $variant_response"
    
    if [ "$variant_response" = "200" ]; then
        echo "  ✅ This job name works!"
        WORKING_JOB_NAME="$variant"
        break
    fi
done

# Test 5: Try build trigger without authentication token (if job accessible)
if [ -n "$WORKING_JOB_NAME" ]; then
    echo -e "\n5. Testing build trigger without build token..."
    no_token_response=$(curl -s -w "%{http_code}" -X POST \
        -u "$USERNAME:$API_TOKEN" \
        -o /tmp/no_token_test.txt \
        "$JENKINS_URL/job/$WORKING_JOB_NAME/build")
    echo "Build without token response: $no_token_response"
    
    if [ "$no_token_response" = "201" ] || [ "$no_token_response" = "200" ]; then
        echo "✅ Build trigger works without build token!"
        echo "Working command:"
        echo "curl -X POST -u '$USERNAME:$API_TOKEN' '$JENKINS_URL/job/$WORKING_JOB_NAME/build'"
    else
        echo "❌ Build trigger failed: $no_token_response"
        cat /tmp/no_token_test.txt
    fi
fi

echo -e "\n====================================="
echo "TROUBLESHOOTING RECOMMENDATIONS:"
echo "====================================="

if [ "$api_response" = "401" ]; then
    echo "🔧 API TOKEN ISSUE:"
    echo "1. The API token might be incorrect or expired"
    echo "2. Try regenerating the API token:"
    echo "   - Go to: $JENKINS_URL/user/$USERNAME/configure"
    echo "   - Delete old tokens and create a new one"
    echo "3. Make sure you're using the correct username: '$USERNAME'"
    echo "4. Try using the actual password instead of API token"
fi

if [ "$job_response" = "404" ]; then
    echo "🔧 JOB NAME ISSUE:"
    echo "1. Check the exact job name in Jenkins dashboard"
    echo "2. Job names are case-sensitive"
    echo "3. Check for spaces, underscores, or special characters"
fi

if [ "$job_response" = "403" ]; then
    echo "🔧 PERMISSION ISSUE:"
    echo "1. User '$USERNAME' needs 'Job/Build' permission"
    echo "2. Check Jenkins security settings:"
    echo "   - Go to: $JENKINS_URL/manage/configureSecurity/"
    echo "   - Ensure user has proper permissions"
    echo "3. Check job-specific permissions if using project-based security"
fi

echo -e "\n🔍 NEXT STEPS:"
echo "1. Verify the API token is correct"
echo "2. Check user permissions in Jenkins"
echo "3. Confirm the exact job name"
echo "4. Try using password instead of API token"

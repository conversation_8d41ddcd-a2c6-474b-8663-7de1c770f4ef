# 🎯 JENKINS REMOTE TRIGGER - FINAL SOLUTION

## 🚨 **ROOT CAUSE FOUND**
Your Jenkins remote trigger is failing because:
1. **Invalid API Token**: The token `1153dd0473f7369377a30eb715e637b493` returns `401 Unauthorized`
2. **Authentication Required**: <PERSON> requires valid credentials for all API calls

## ✅ **STEP-BY-STEP FIX**

### Step 1: Generate New API Token
1. **Login to <PERSON>**: `http://135.236.218.20:81`
2. **Go to User Profile**: Click "Takaful Admin" (top right) → "Configure"
3. **Create API Token**:
   - Find "API Token" section
   - Click "Add new Token"
   - Name: "Remote Trigger Token"
   - Click "Generate"
   - **COPY THE TOKEN IMMEDIATELY** ⚠️

### Step 2: Test the New Token
```bash
# Replace YOUR_NEW_API_TOKEN with the token from Step 1
curl -X POST -u 'devops:YOUR_NEW_API_TOKEN' \
  'http://135.236.218.20:81/job/Takaful%20Webform%20dev/build?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw'
```

**Expected Result**: HTTP 201 (Created) or HTTP 200 (OK)

### Step 3: Alternative - Use Password Instead
If API token doesn't work, try with the actual password:
```bash
# Replace YOUR_PASSWORD with the Jenkins login password
curl -X POST -u 'devops:YOUR_PASSWORD' \
  'http://135.236.218.20:81/job/Takaful%20Webform%20dev/build?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw'
```

## 🔧 **WORKING COMMAND TEMPLATE**

Once you have a valid API token:

```bash
#!/bin/bash
JENKINS_URL="http://135.236.218.20:81"
USERNAME="devops"
API_TOKEN="YOUR_NEW_API_TOKEN_HERE"
JOB_NAME="Takaful%20Webform%20dev"
BUILD_TOKEN="8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw"

# Trigger the build
curl -X POST -u "$USERNAME:$API_TOKEN" \
  "$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN"
```

## 🌐 **FOR WEBHOOKS**

### BitBucket Webhook URL:
```
**************************************************/job/Takaful%20Webform%20dev/build?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw
```

### GitHub Webhook:
```
URL: **************************************************/github-webhook/
Content-Type: application/json
```

## 🔍 **VERIFICATION STEPS**

1. **Test Authentication**:
   ```bash
   curl -u 'devops:YOUR_NEW_API_TOKEN' 'http://135.236.218.20:81/api/json'
   ```
   Should return JSON (not 401 error)

2. **Test Job Access**:
   ```bash
   curl -u 'devops:YOUR_NEW_API_TOKEN' 'http://135.236.218.20:81/job/Takaful%20Webform%20dev/api/json'
   ```
   Should return job details

3. **Test Build Trigger**:
   ```bash
   curl -X POST -u 'devops:YOUR_NEW_API_TOKEN' \
     'http://135.236.218.20:81/job/Takaful%20Webform%20dev/build?token=8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw'
   ```
   Should return HTTP 201/200

## 🚨 **TROUBLESHOOTING**

### If you still get 401 Unauthorized:
1. **Check username**: Ensure "devops" is correct
2. **Verify permissions**: User needs "Job/Build" permission
3. **Try different user**: Use admin account if available

### If you get 403 Forbidden:
1. **Check job configuration**: Ensure "Trigger builds remotely" is enabled
2. **Verify build token**: Check if `8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw` is correct

### If you get 404 Not Found:
1. **Check job name**: Try different encodings:
   - `Takaful%20Webform%20dev`
   - `Takaful+Webform+dev`
   - `Takaful%2520Webform%2520dev`

## 🎯 **SUMMARY**
The issue is **100% authentication-related**. Once you generate a valid API token and use it in your requests, the remote trigger will work perfectly!

**Next Action**: Generate new API token and test with the commands above.

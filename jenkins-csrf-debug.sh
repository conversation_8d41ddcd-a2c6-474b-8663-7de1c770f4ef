#!/bin/bash

# Jenkins CSRF Debug and Alternative Solutions

JENKINS_URL="http://135.236.218.20:81"
JOB_NAME="Takaful_Webform_dev"
BUILD_TOKEN="8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw"

echo "🔍 Jenkins CSRF Debug and Solutions"
echo "=================================="

# Test 1: Check what crumb endpoint returns
echo -e "\n1. Testing crumb endpoints..."

echo "XML crumb endpoint:"
xml_crumb=$(curl -s "$JENKINS_URL/crumbIssuer/api/xml?xpath=concat(//crumbRequestField,\":\",//crumb)")
echo "Response: $xml_crumb"

echo -e "\nJSON crumb endpoint:"
json_crumb=$(curl -s "$JENKINS_URL/crumbIssuer/api/json")
echo "Response: $json_crumb"

# Test 2: Try with session cookies
echo -e "\n2. Testing with session cookies..."
cookie_jar="/tmp/jenkins_cookies.txt"

# First, get a session by visiting the main page
curl -s -c "$cookie_jar" "$JENKINS_URL/" > /dev/null

# Then get crumb with session
session_crumb=$(curl -s -b "$cookie_jar" "$JENKINS_URL/crumbIssuer/api/xml?xpath=concat(//crumbRequestField,\":\",//crumb)")
echo "Session crumb: $session_crumb"

if [[ $session_crumb == *":"* ]]; then
    crumb_header=$(echo "$session_crumb" | cut -d: -f1)
    crumb_value=$(echo "$session_crumb" | cut -d: -f2)
    
    echo "Trying build with session cookies and crumb..."
    session_response=$(curl -s -w "%{http_code}" -X POST \
        -b "$cookie_jar" \
        -H "$crumb_header: $crumb_value" \
        -o /tmp/session_response.txt \
        "$JENKINS_URL/job/$JOB_NAME/buildWithParameters?token=$BUILD_TOKEN")
    
    echo "Session response: $session_response"
    if [ "$session_response" = "201" ] || [ "$session_response" = "200" ]; then
        echo "✅ SUCCESS with session cookies!"
        exit 0
    else
        echo "Session response content:"
        cat /tmp/session_response.txt | head -10
    fi
fi

# Test 3: Try GET request (sometimes works for simple triggers)
echo -e "\n3. Testing GET request..."
get_response=$(curl -s -w "%{http_code}" \
    -o /tmp/get_response.txt \
    "$JENKINS_URL/job/$JOB_NAME/buildWithParameters?token=$BUILD_TOKEN")

echo "GET response: $get_response"
if [ "$get_response" = "200" ]; then
    echo "✅ GET request works! (Check if build was triggered)"
    cat /tmp/get_response.txt | head -5
elif [ "$get_response" = "302" ]; then
    echo "✅ GET request redirected (might have worked)"
else
    echo "GET response content:"
    cat /tmp/get_response.txt | head -10
fi

# Test 4: Try with different User-Agent
echo -e "\n4. Testing with browser User-Agent..."
ua_response=$(curl -s -w "%{http_code}" -X POST \
    -H "User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36" \
    -H "$crumb_header: $crumb_value" \
    -o /tmp/ua_response.txt \
    "$JENKINS_URL/job/$JOB_NAME/buildWithParameters?token=$BUILD_TOKEN")

echo "User-Agent response: $ua_response"
if [ "$ua_response" = "201" ] || [ "$ua_response" = "200" ]; then
    echo "✅ SUCCESS with User-Agent!"
else
    cat /tmp/ua_response.txt | head -5
fi

# Test 5: Check if we can access the job configuration
echo -e "\n5. Testing job configuration access..."
config_response=$(curl -s -w "%{http_code}" \
    -o /tmp/config_response.txt \
    "$JENKINS_URL/job/$JOB_NAME/config.xml")

echo "Config access response: $config_response"
if [ "$config_response" = "200" ]; then
    echo "✅ Can access job config (anonymous access enabled)"
    # Check if remote trigger is enabled
    if grep -q "authToken" /tmp/config_response.txt; then
        echo "✅ Remote trigger is configured in job"
        auth_token=$(grep -o "<authToken>[^<]*</authToken>" /tmp/config_response.txt | sed 's/<[^>]*>//g')
        echo "Configured token: $auth_token"
        if [ "$auth_token" = "$BUILD_TOKEN" ]; then
            echo "✅ Token matches!"
        else
            echo "❌ Token mismatch!"
        fi
    else
        echo "❌ Remote trigger not found in job config"
    fi
fi

echo -e "\n=================================="
echo "🔧 ALTERNATIVE SOLUTIONS:"
echo "=================================="

echo "1. DISABLE CSRF PROTECTION (if you have admin access):"
echo "   - Go to: $JENKINS_URL/manage/configureSecurity/"
echo "   - Uncheck 'Prevent Cross Site Request Forgery exploits'"
echo "   - Save configuration"
echo "   - Then use: curl -X POST '$JENKINS_URL/job/$JOB_NAME/buildWithParameters?token=$BUILD_TOKEN'"

echo -e "\n2. USE JENKINS CLI:"
echo "   wget $JENKINS_URL/jnlpJars/jenkins-cli.jar"
echo "   java -jar jenkins-cli.jar -s $JENKINS_URL build '$JOB_NAME' -p token=$BUILD_TOKEN"

echo -e "\n3. USE WEBHOOK PLUGIN:"
echo "   - Install 'Generic Webhook Trigger' plugin"
echo "   - Configure webhook without CSRF protection"

echo -e "\n4. BROWSER AUTOMATION:"
echo "   - Use tools like Selenium to automate browser clicks"
echo "   - Navigate to build page and click 'Build' button"

echo -e "\n5. CHECK BUILD QUEUE:"
echo "   - Go to: $JENKINS_URL"
echo "   - Check if any builds are queued or running"
echo "   - Some of the above methods might have worked"

rm -f "$cookie_jar" /tmp/*_response.txt

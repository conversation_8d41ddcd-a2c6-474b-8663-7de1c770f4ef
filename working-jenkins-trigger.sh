#!/bin/bash

# Working Jenkins Remote Trigger Script
# Using the new API token: 167bd81b88bce0b5fbde9a9466fe76f0

JENKINS_URL="http://**************:81"
USERNAME="devops"
API_TOKEN="167bd81b88bce0b5fbde9a9466fe76f0"
JOB_NAME="Takaful%20Webform%20dev"
BUILD_TOKEN="8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw"

echo "Testing Jenkins Remote Trigger with New API Token"
echo "================================================"

# Method 1: Simple POST without crumb (try first)
echo -e "\n1. Testing simple POST request..."
response1=$(curl -s -w "%{http_code}" -X POST \
    -u "$USERNAME:$API_TOKEN" \
    -o /tmp/jenkins_simple.txt \
    "$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN")

echo "Response Code: $response1"

if [ "$response1" = "201" ] || [ "$response1" = "200" ]; then
    echo "✅ SUCCESS! Simple remote trigger works!"
    echo ""
    echo "🎉 YOUR WORKING COMMAND:"
    echo "curl -X POST -u '$USERNAME:$API_TOKEN' \\"
    echo "  '$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN'"
    exit 0
elif [ "$response1" = "403" ]; then
    echo "❌ 403 Forbidden - trying with CSRF crumb..."
    cat /tmp/jenkins_simple.txt | head -5
else
    echo "❌ Unexpected response: $response1"
    cat /tmp/jenkins_simple.txt | head -5
fi

# Method 2: Get crumb properly and use it
echo -e "\n2. Getting CSRF crumb..."

# Get crumb with proper parsing
crumb_data=$(curl -s -u "$USERNAME:$API_TOKEN" \
    "$JENKINS_URL/crumbIssuer/api/json")

echo "Crumb data received: ${crumb_data:0:50}..."

if [[ $crumb_data == *"crumb"* ]]; then
    # Extract crumb value from JSON
    crumb_value=$(echo "$crumb_data" | grep -o '"crumb":"[^"]*"' | cut -d'"' -f4)
    crumb_field=$(echo "$crumb_data" | grep -o '"crumbRequestField":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$crumb_value" ] && [ -n "$crumb_field" ]; then
        echo "Crumb field: $crumb_field"
        echo "Crumb value: ${crumb_value:0:20}..."
        
        # Make request with crumb
        echo -e "\n3. Making request with CSRF crumb..."
        response2=$(curl -s -w "%{http_code}" -X POST \
            -u "$USERNAME:$API_TOKEN" \
            -H "$crumb_field: $crumb_value" \
            -o /tmp/jenkins_crumb.txt \
            "$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN")
        
        echo "Response Code with crumb: $response2"
        
        if [ "$response2" = "201" ] || [ "$response2" = "200" ]; then
            echo "✅ SUCCESS! Remote trigger with CSRF crumb works!"
            echo ""
            echo "🎉 YOUR WORKING COMMAND:"
            echo "# Get crumb:"
            echo "CRUMB_DATA=\$(curl -s -u '$USERNAME:$API_TOKEN' '$JENKINS_URL/crumbIssuer/api/json')"
            echo "CRUMB_VALUE=\$(echo \"\$CRUMB_DATA\" | grep -o '\"crumb\":\"[^\"]*\"' | cut -d'\"' -f4)"
            echo "CRUMB_FIELD=\$(echo \"\$CRUMB_DATA\" | grep -o '\"crumbRequestField\":\"[^\"]*\"' | cut -d'\"' -f4)"
            echo ""
            echo "# Trigger build:"
            echo "curl -X POST -u '$USERNAME:$API_TOKEN' \\"
            echo "  -H \"\$CRUMB_FIELD: \$CRUMB_VALUE\" \\"
            echo "  '$JENKINS_URL/job/$JOB_NAME/build?token=$BUILD_TOKEN'"
            exit 0
        else
            echo "❌ CSRF crumb method failed: $response2"
            cat /tmp/jenkins_crumb.txt | head -5
        fi
    else
        echo "❌ Could not extract crumb values"
    fi
else
    echo "❌ Could not get crumb data"
fi

# Method 3: Try buildWithParameters
echo -e "\n4. Testing buildWithParameters..."
response3=$(curl -s -w "%{http_code}" -X POST \
    -u "$USERNAME:$API_TOKEN" \
    -o /tmp/jenkins_params.txt \
    "$JENKINS_URL/job/$JOB_NAME/buildWithParameters?token=$BUILD_TOKEN&cause=Remote+Test")

echo "Response Code for buildWithParameters: $response3"

if [ "$response3" = "201" ] || [ "$response3" = "200" ]; then
    echo "✅ SUCCESS! buildWithParameters works!"
    echo ""
    echo "🎉 YOUR WORKING COMMAND:"
    echo "curl -X POST -u '$USERNAME:$API_TOKEN' \\"
    echo "  '$JENKINS_URL/job/$JOB_NAME/buildWithParameters?token=$BUILD_TOKEN&cause=Remote+Trigger'"
    exit 0
else
    echo "❌ buildWithParameters failed: $response3"
    cat /tmp/jenkins_params.txt | head -5
fi

# Method 4: Try without build token
echo -e "\n5. Testing without build token..."
response4=$(curl -s -w "%{http_code}" -X POST \
    -u "$USERNAME:$API_TOKEN" \
    -o /tmp/jenkins_no_token.txt \
    "$JENKINS_URL/job/$JOB_NAME/build")

echo "Response Code without build token: $response4"

if [ "$response4" = "201" ] || [ "$response4" = "200" ]; then
    echo "✅ SUCCESS! Works without build token!"
    echo ""
    echo "🎉 YOUR WORKING COMMAND:"
    echo "curl -X POST -u '$USERNAME:$API_TOKEN' \\"
    echo "  '$JENKINS_URL/job/$JOB_NAME/build'"
    exit 0
else
    echo "❌ Without build token failed: $response4"
    cat /tmp/jenkins_no_token.txt | head -5
fi

echo -e "\n================================================"
echo "❌ All methods failed. Check Jenkins job configuration:"
echo "1. Go to: $JENKINS_URL/job/$JOB_NAME/configure"
echo "2. Ensure 'Trigger builds remotely' is checked"
echo "3. Verify build token is correct"
echo "4. Check user permissions"

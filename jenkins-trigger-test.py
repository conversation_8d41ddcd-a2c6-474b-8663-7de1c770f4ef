#!/usr/bin/env python3
"""
Jenkins Remote Trigger Test Script
Tests various aspects of <PERSON> remote trigger functionality on AKS
"""

import requests
import urllib.parse
import json
import sys
from datetime import datetime

class JenkinsRemoteTriggerTester:
    def __init__(self, jenkins_url, job_name, token):
        self.jenkins_url = jenkins_url.rstrip('/')
        self.job_name = job_name
        self.token = token
        self.session = requests.Session()
        self.session.timeout = 30
        
    def test_connectivity(self):
        """Test basic connectivity to Jenkins server"""
        print("1. Testing Jenkins server connectivity...")
        try:
            response = self.session.get(self.jenkins_url)
            if response.status_code == 200:
                print("✓ Jenkins server is reachable")
                return True
            else:
                print(f"✗ Jenkins server returned status code: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ Cannot reach Jenkins server: {e}")
            return False
    
    def test_job_exists(self):
        """Check if the job exists"""
        print("\n2. Checking if job exists...")
        
        # Try different URL encodings
        job_variants = [
            self.job_name,
            urllib.parse.quote(self.job_name, safe=''),
            urllib.parse.quote_plus(self.job_name)
        ]
        
        for variant in job_variants:
            url = f"{self.jenkins_url}/job/{variant}/api/json"
            print(f"   Trying: {url}")
            
            try:
                response = self.session.get(url)
                if response.status_code == 200:
                    print(f"✓ Job found with encoding: {variant}")
                    self.job_name = variant  # Use the working variant
                    return True
                elif response.status_code == 404:
                    print(f"   Job not found with this encoding")
                else:
                    print(f"   Unexpected status: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"   Request failed: {e}")
        
        print("✗ Job not found with any encoding variant")
        return False
    
    def test_remote_trigger(self):
        """Test the remote trigger functionality"""
        print("\n3. Testing remote trigger...")
        
        # Basic trigger URL
        trigger_url = f"{self.jenkins_url}/job/{self.job_name}/build"
        params = {'token': self.token}
        
        print(f"   Trigger URL: {trigger_url}")
        print(f"   Token: {self.token[:10]}...")
        
        try:
            response = self.session.post(trigger_url, params=params)
            print(f"   HTTP Status: {response.status_code}")
            
            if response.status_code in [200, 201]:
                print("✓ Remote trigger successful!")
                return True
            elif response.status_code == 403:
                print("✗ Authentication failed - check token or job permissions")
                print("   Make sure 'Trigger builds remotely' is enabled in job configuration")
            elif response.status_code == 404:
                print("✗ Job not found - check job name")
            else:
                print(f"✗ Unexpected response: {response.status_code}")
                print(f"   Response: {response.text[:200]}")
            
            return False
            
        except requests.exceptions.RequestException as e:
            print(f"✗ Request failed: {e}")
            return False
    
    def test_with_parameters(self):
        """Test trigger with parameters"""
        print("\n4. Testing trigger with parameters...")
        
        trigger_url = f"{self.jenkins_url}/job/{self.job_name}/buildWithParameters"
        params = {
            'token': self.token,
            'cause': 'Remote trigger test'
        }
        
        try:
            response = self.session.post(trigger_url, params=params)
            print(f"   HTTP Status: {response.status_code}")
            
            if response.status_code in [200, 201]:
                print("✓ Parameterized trigger successful!")
                return True
            else:
                print(f"✗ Parameterized trigger failed: {response.status_code}")
            
        except requests.exceptions.RequestException as e:
            print(f"✗ Parameterized trigger request failed: {e}")
        
        return False
    
    def run_all_tests(self):
        """Run all tests"""
        print(f"Jenkins Remote Trigger Test - {datetime.now()}")
        print("=" * 50)
        
        results = []
        results.append(self.test_connectivity())
        results.append(self.test_job_exists())
        results.append(self.test_remote_trigger())
        results.append(self.test_with_parameters())
        
        print("\n" + "=" * 50)
        print("Test Summary:")
        test_names = ["Connectivity", "Job Exists", "Remote Trigger", "Parameterized Trigger"]
        
        for i, (name, result) in enumerate(zip(test_names, results)):
            status = "✓ PASS" if result else "✗ FAIL"
            print(f"  {name}: {status}")
        
        passed = sum(results)
        total = len(results)
        print(f"\nOverall: {passed}/{total} tests passed")
        
        return passed == total

def main():
    # Configuration
    JENKINS_URL = "http://**************:81"
    JOB_NAME = "Takaful Webform dev"
    TOKEN = "8u2RRJT7AmfgAUJc9zSCCKMhhyCeBsOU9pAcgR8MaHo977fHte7YCZ6fjfio0tpw"
    
    tester = JenkinsRemoteTriggerTester(JENKINS_URL, JOB_NAME, TOKEN)
    success = tester.run_all_tests()
    
    if not success:
        print("\nTroubleshooting Tips:")
        print("1. Verify Jenkins is running and accessible")
        print("2. Check job name spelling and encoding")
        print("3. Ensure 'Trigger builds remotely' is enabled in job config")
        print("4. Verify the authentication token is correct")
        print("5. Check Jenkins security settings and permissions")
        print("6. Ensure AKS ingress/load balancer is properly configured")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
